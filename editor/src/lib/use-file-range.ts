import { useSelector } from '@topwrite/common';
import { useCallback } from 'react';
import { Range } from 'slate';

export default function useFileRange() {
    const { instance, serializer } = useSelector('editor');
    const { current: file } = useSelector('files');

    return useCallback(() => {
        const selection = instance?.selection;
        if (file && selection && serializer) {
            if (Range.isCollapsed(selection)) {
                return null;
            }
            const content = file.content.toString();

            const descendants = serializer.deserialize(content);

            const [start, end] = Range.edges(selection);
            // 获取选区开始位置的信息
            const startPosition = getPositionFromPoint(descendants, start.path, start.offset);
            if (!startPosition) return null;

            // 获取选区结束位置的信息
            const endPosition = getPositionFromPoint(descendants, end.path, end.offset);
            if (!endPosition) return null;

            // 获取选中区域的实际文本内容
            const selectedText = getTextFromFileRange(content, startPosition, endPosition);

            return {
                start: startPosition,
                end: endPosition,
                text: selectedText
            };
        }

        return null;
    }, [instance, serializer, file]);
}


function getTextFromFileRange(content: string, start: FilePosition, end: FilePosition): string {
    try {
        const lines = content.split('\n');

        // 如果开始和结束在同一行
        if (start.line === end.line) {
            const line = lines[start.line - 1]; // 行号从1开始，数组从0开始
            if (line) {
                return line.substring(start.column - 1, end.column - 1); // 列号从1开始
            }
            return '';
        }

        // 跨多行的情况
        let result = '';

        // 第一行：从开始列到行尾
        const firstLine = lines[start.line - 1];
        if (firstLine !== undefined) {
            result += firstLine.substring(start.column - 1);
            // 只有当不是最后一行时才添加换行符
            if (start.line < end.line) {
                result += '\n';
            }
        }

        // 中间的完整行
        for (let i = start.line; i < end.line - 1; i++) {
            if (lines[i] !== undefined) {
                result += lines[i] + '\n';
            }
        }

        // 最后一行：从行首到结束列
        if (end.line > start.line) {
            const lastLine = lines[end.line - 1];
            if (lastLine !== undefined) {
                result += lastLine.substring(0, end.column - 1);
            }
        }

        return result;
    } catch (error) {
        console.error('获取文件范围文本时出错:', error);
        return '';
    }
}
