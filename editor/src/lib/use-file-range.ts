import { useSelector } from '@topwrite/common';
import { useCallback } from 'react';
import { Editor, Element, Node, Path, Range, Text } from 'slate';

export default function useFileRange() {
    const { instance, serializer } = useSelector('editor');
    const { current: file } = useSelector('files');

    return useCallback(() => {
        const selection = instance?.selection;
        if (file && selection && serializer) {
            if (Range.isCollapsed(selection)) {
                return null;
            }
            const content = file.content.toString();

            const descendants = serializer.deserialize(content);

            const editor: any = { children: descendants };

            const [start, end] = Range.edges(selection);

            console.log(start, descendants);
            // 获取选区开始位置的信息
            const startPosition = getPositionFromPoint(editor, start.path, start.offset);
            if (!startPosition) return null;

            // 获取选区结束位置的信息
            const endPosition = getPositionFromPoint(editor, end.path, end.offset);
            if (!endPosition) return null;

            // 获取选中区域的实际文本内容
            const selectedText = getTextFromFileRange(content, startPosition, endPosition);

            return {
                start: startPosition,
                end: endPosition,
                text: selectedText
            };
        }

        return null;
    }, [instance, serializer, file]);
}


function getTextFromFileRange(content: string, start: FilePosition, end: FilePosition): string {
    try {
        const lines = content.split('\n');

        // 如果开始和结束在同一行
        if (start.line === end.line) {
            const line = lines[start.line - 1]; // 行号从1开始，数组从0开始
            if (line) {
                return line.substring(start.column - 1, end.column - 1); // 列号从1开始
            }
            return '';
        }

        // 跨多行的情况
        let result = '';

        // 第一行：从开始列到行尾
        const firstLine = lines[start.line - 1];
        if (firstLine !== undefined) {
            result += firstLine.substring(start.column - 1);
            // 只有当不是最后一行时才添加换行符
            if (start.line < end.line) {
                result += '\n';
            }
        }

        // 中间的完整行
        for (let i = start.line; i < end.line - 1; i++) {
            if (lines[i] !== undefined) {
                result += lines[i] + '\n';
            }
        }

        // 最后一行：从行首到结束列
        if (end.line > start.line) {
            const lastLine = lines[end.line - 1];
            if (lastLine !== undefined) {
                result += lastLine.substring(0, end.column - 1);
            }
        }

        return result;
    } catch (error) {
        console.error('获取文件范围文本时出错:', error);
        return '';
    }
}

function getPositionFromPoint(editor: Editor, path: Path, offset: number): FilePosition | null {
    try {
        // 根据路径获取节点
        const node = Node.get(editor, path);

        // 如果是文本节点且有位置信息
        if (Text.isText(node) && node.position) {
            const { position } = node;
            const text = node.text;

            // 如果偏移量为0，返回开始位置
            if (offset === 0) {
                return {
                    line: position.start.line,
                    column: position.start.column
                };
            }

            // 如果偏移量超出文本长度，返回结束位置
            if (offset >= text.length) {
                return {
                    line: position.end.line,
                    column: position.end.column
                };
            }

            // 计算偏移量对应的实际位置
            const beforeOffset = text.substring(0, offset);
            const lines = beforeOffset.split('\n');
            const lineOffset = lines.length - 1;
            const columnOffset = lines[lines.length - 1].length;

            return {
                line: position.start.line + lineOffset,
                column: lineOffset === 0 ? position.start.column + columnOffset : columnOffset + 1
            };
        }

        // 处理没有位置信息的文本节点
        if (Text.isText(node) && !node.position) {
            // 尝试从父节点获取位置信息
            if (path.length > 0) {
                const parentPath = Path.parent(path);
                const parentNode = Node.get(editor, parentPath);

                // 如果父节点是 codeLine，继续向上查找 code 节点
                if (Element.isElement(parentNode) && parentNode.type === 'codeLine') {
                    const codeLinePath = parentPath;
                    const codeLineIndex = codeLinePath[codeLinePath.length - 1];

                    if (codeLinePath.length > 0) {
                        const codePath = Path.parent(codeLinePath);
                        const codeNode = Node.get(editor, codePath);

                        // 如果是代码块节点且有位置信息
                        if (Element.isElement(codeNode) && codeNode.type === 'code' && codeNode.position) {
                            const { position } = codeNode;
                            // 代码块的实际内容从 position.start.line + 1 开始（跳过开头的 ```）
                            // 计算在代码块中的实际行号
                            const actualLine = position.start.line + 1 + codeLineIndex;

                            // 确保不超过代码块的结束行（减去结尾的 ```）
                            const maxLine = position.end.line - 1;
                            const finalLine = Math.min(actualLine, maxLine);

                            return {
                                line: finalLine,
                                column: 1 + offset // 代码行从第1列开始，加上偏移量
                            };
                        }
                    }
                }

                // 如果父节点是 paragraph 或其他有位置信息的元素
                if (Element.isElement(parentNode) && parentNode.position) {
                    const { position } = parentNode;

                    // 计算在父元素中的偏移量
                    // 需要考虑同级的其他文本节点
                    let textOffset = 0;
                    const textIndex = path[path.length - 1];

                    // 累加前面兄弟文本节点的长度
                    for (let i = 0; i < textIndex; i++) {
                        const siblingPath = [...parentPath, i];
                        try {
                            const siblingNode = Node.get(editor, siblingPath);
                            if (Text.isText(siblingNode)) {
                                textOffset += siblingNode.text.length;
                            }
                        } catch {
                            // 忽略无效路径
                        }
                    }

                    // 加上当前节点内的偏移量
                    const totalOffset = textOffset + offset;

                    // 根据总偏移量计算实际位置
                    const allText = Node.string(parentNode);
                    const beforeOffset = allText.substring(0, totalOffset);
                    const lines = beforeOffset.split('\n');
                    const lineOffset = lines.length - 1;
                    const columnOffset = lines[lines.length - 1].length;

                    return {
                        line: position.start.line + lineOffset,
                        column: lineOffset === 0 ? position.start.column + columnOffset : columnOffset + 1
                    };
                }
            }
        }

        // 如果是元素节点且有位置信息
        if (Element.isElement(node) && node.position) {
            const { position } = node;

            // 如果偏移量为0，返回开始位置
            if (offset === 0) {
                return {
                    line: position.start.line,
                    column: position.start.column
                };
            }

            // 对于元素节点，如果有偏移量，返回结束位置
            return {
                line: position.end.line,
                column: position.end.column
            };
        }

        // 尝试从父节点获取位置信息
        if (path.length > 0) {
            const parentPath = Path.parent(path);
            const parentNode = Node.get(editor, parentPath);

            if (Element.isElement(parentNode) && parentNode.position) {
                return {
                    line: parentNode.position.start.line,
                    column: parentNode.position.start.column
                };
            }
        }

        return null;
    } catch (error) {
        console.error('获取节点位置时出错:', error);
        return null;
    }
}
