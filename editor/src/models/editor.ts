import PlatePlugin, { Decorate } from '../components/editor/plate-plugin';
import { Model } from '@topwrite/common';
import { enableMapSet } from 'immer';
import { Editor as SlateEditor } from 'slate';
import Serializer from '../components/editor/serializer';

enableMapSet();

interface EditorType {
    plugins: Map<PlatePlugin, PlatePlugin>;
    decorates: Map<Decorate, Decorate>;
    instance: SlateEditor | null;
    serializer: Serializer | null;
}

class Editor extends Model<EditorType> {

    initialState = {
        plugins: new Map(),
        decorates: new Map(),
        instance: null,
        serializer: null
    };

    *setSerializer(serializer: Serializer | null) {
        yield this.setState(state => {
            state.serializer = serializer;
        });
    }

    *setInstance(instance: SlateEditor | null) {
        yield this.setState(state => {
            state.instance = instance;
        });
    }

    *addPlugin(plugin: PlatePlugin) {
        yield this.setState(({ plugins }) => {
            plugins.set(plugin, plugin);
        });
    }

    *removePlugin(plugin: PlatePlugin) {
        yield this.setState(({ plugins }) => {
            plugins.delete(plugin);
        });
    }

    *addDecorate(decorate: Decorate) {
        yield this.setState(({ decorates }) => {
            decorates.set(decorate, decorate);
        });
    }

    *removeDecorate(decorate: Decorate) {
        yield this.setState(({ decorates }) => {
            decorates.delete(decorate);
        });
    }
}

export const editor = new Editor();
