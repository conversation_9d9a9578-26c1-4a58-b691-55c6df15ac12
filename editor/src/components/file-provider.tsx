import { useActions } from '@topwrite/common';
import { createContext, PropsWithChildren, useContext, useEffect } from 'react';
import File from '../entities/file';

export const FileContext = createContext<File | undefined>(undefined);

interface Props {
    file?: File;
}

export default function FileProvider({ file, children }: PropsWithChildren<Props>) {
    const { setCurrent } = useActions('files');

    useEffect(() => {
        if (file) {
            setCurrent(file);
            return () => {
                setCurrent(null);
            };
        }
    }, [file]);

    if (!file) {
        return null;
    }
    return <FileContext.Provider value={file}>
        {children}
    </FileContext.Provider>;
}

export function useFile() {
    const file = useContext(FileContext);
    if (!file) {
        throw new Error('file object not initialized');
    }
    return file;
}
