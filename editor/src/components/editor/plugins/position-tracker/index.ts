import { Editor, Operation } from 'slate';
import { updatePositionsOnChange, resetPositionManager } from '../../utils/position-calculator';

/**
 * 位置跟踪插件
 * 监听编辑器变更并实时更新位置信息
 */
export const withPositionTracker = (editor: Editor) => {
    const { apply } = editor;

    // 重写 apply 方法来监听所有操作
    editor.apply = (operation: Operation) => {
        // 先执行原始操作
        apply(operation);

        // 然后更新位置信息
        updatePositionsOnChange(editor, operation);
    };

    return editor;
};

/**
 * 位置跟踪器配置选项
 */
export interface PositionTrackerOptions {
    /**
     * 是否启用实时位置跟踪
     */
    enabled?: boolean;
    
    /**
     * 位置变更记录的最大保存时间（毫秒）
     */
    maxAge?: number;
    
    /**
     * 是否在文档加载时重置位置管理器
     */
    resetOnLoad?: boolean;
}

/**
 * 初始化位置跟踪器
 */
export function initializePositionTracker(options: PositionTrackerOptions = {}) {
    const {
        enabled = true,
        maxAge = 300000, // 5分钟
        resetOnLoad = true
    } = options;

    if (!enabled) {
        return;
    }

    // 如果需要在加载时重置
    if (resetOnLoad) {
        resetPositionManager();
    }

    // 定期清理过期记录
    setInterval(() => {
        const { cleanupPositionManager } = require('../../utils/position-calculator');
        cleanupPositionManager();
    }, maxAge / 2);
}

/**
 * 位置跟踪器工具函数
 */
export const PositionTracker = {
    /**
     * 检查编辑器是否启用了位置跟踪
     */
    isEnabled: (editor: Editor): boolean => {
        return 'positionTracker' in editor;
    },

    /**
     * 为编辑器启用位置跟踪
     */
    enable: (editor: Editor, options?: PositionTrackerOptions): Editor => {
        if (PositionTracker.isEnabled(editor)) {
            return editor;
        }

        const trackedEditor = withPositionTracker(editor);
        (trackedEditor as any).positionTracker = true;
        
        initializePositionTracker(options);
        
        return trackedEditor;
    },

    /**
     * 重置位置跟踪器
     */
    reset: () => {
        resetPositionManager();
    }
};

export default PositionTracker;
